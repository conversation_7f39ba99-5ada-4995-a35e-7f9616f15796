{"1": {"inputs": {"text": "", "clip": ["90", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "3": {"inputs": {"clip_name": "siglip2_so400m_patch16_512.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "6": {"inputs": {"crop": "none", "clip_vision": ["3", 0], "image": ["88", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "7": {"inputs": {"style_weight": 1.2000000000000002, "color_weight": 1.2000000000000002, "content_weight": 1.2000000000000002, "structure_weight": 1, "texture_weight": 1, "similarity_threshold": 1, "enhancement_base": 1.5, "conditioning": ["8", 0], "style_model": ["45", 0], "clip_vision_output": ["6", 0]}, "class_type": "StyleModelAdvancedApply", "_meta": {"title": "Style Model Advanced Apply"}}, "8": {"inputs": {"downsampling_factor": 3, "downsampling_function": "area", "mode": "center crop (square)", "weight": 0.25, "autocrop_margin": 0.1, "conditioning": ["70", 0], "style_model": ["45", 0], "clip_vision": ["3", 0], "image": ["88", 0]}, "class_type": "ReduxAdvanced", "_meta": {"title": "ReduxAdvanced"}}, "15": {"inputs": {"add_noise": true, "noise_seed": 776098249434852, "steps": 30, "cfg": 1, "sampler_name": "euler", "scheduler": "sgm_uniform", "start_at_step": 0, "end_at_step": 10000, "noise_mode": "GPU(=A1111)", "return_with_leftover_noise": false, "batch_seed_mode": "incremental", "variation_seed": 0, "variation_strength": 0, "variation_method": "linear", "internal_seed": 0, "model": ["39", 0], "positive": ["80", 0], "negative": ["38", 0], "latent_image": ["41", 0]}, "class_type": "KSamplerAdvanced //Inspire", "_meta": {"title": "KSamplerAdvanced (inspire)"}}, "16": {"inputs": {"upscale_model": ["100", 0], "image": ["83", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "17": {"inputs": {"pixels": ["20", 0], "vae": ["81", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "18": {"inputs": {"sharpen_radius": 1, "sigma": 0.4000000000000001, "alpha": 0.4000000000000001, "image": ["16", 0]}, "class_type": "ImageSharpen", "_meta": {"title": "Image Sharpen"}}, "19": {"inputs": {"coeff": 1.2000000000000002, "denoise": 0.3400000000000001}, "class_type": "GITSSchedulerFuncProvider", "_meta": {"title": "GITSScheduler Func Provider"}}, "20": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 3.500000000000001, "image": ["18", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "21": {"inputs": {"samples": ["22", 0], "vae": ["81", 0]}, "class_type": "VAEDecode", "_meta": {"title": "final_image_output"}}, "22": {"inputs": {"add_noise": true, "noise_seed": 1059630169839465, "steps": 25, "cfg": 1, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "sgm_uniform", "start_at_step": 0, "end_at_step": 10000, "noise_mode": "GPU(=A1111)", "return_with_leftover_noise": false, "batch_seed_mode": "incremental", "variation_seed": 0, "variation_strength": 0, "variation_method": "linear", "internal_seed": 0, "model": ["90", 0], "positive": ["70", 0], "negative": ["38", 0], "latent_image": ["27", 0], "scheduler_func_opt": ["19", 0]}, "class_type": "KSamplerAdvanced //Inspire", "_meta": {"title": "KSamplerAdvanced (inspire)"}}, "27": {"inputs": {"noise_std": 0.30000000000000004, "samples": ["17", 0]}, "class_type": "Latent Noise Injection", "_meta": {"title": "Latent Noise Injection"}}, "38": {"inputs": {"conditioning": ["1", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "39": {"inputs": {"max_shift": 1.0000000000000002, "base_shift": 1.0000000000000002, "width": 1024, "height": 1024, "model": ["43", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "41": {"inputs": {"width": 1216, "height": 832, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "43": {"inputs": {"model": ["90", 0]}, "class_type": "CFGZeroStar", "_meta": {"title": "CFGZeroStar"}}, "44": {"inputs": {"samples": ["15", 0], "vae": ["81", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "45": {"inputs": {"style_model_name": "flex1_redux_siglip2_512.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "70": {"inputs": {"guidance": 3.5, "conditioning": ["71", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "71": {"inputs": {"text": ["103", 0], "clip": ["90", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "79": {"inputs": {"anything": ["85", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "80": {"inputs": {"strength": 1.0000000000000002, "conditioning": ["7", 0]}, "class_type": "ConditioningSetAreaStrength", "_meta": {"title": "ConditioningSetAreaStrength"}}, "81": {"inputs": {"vae_name": "Flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "82": {"inputs": {"anything": ["83", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "83": {"inputs": {"brightness": 0, "contrast": 1.1500000000000001, "saturation": 1.1700000000000002, "sharpness": 1, "blur": 0, "gaussian_blur": 0, "edge_enhance": 0, "detail_enhance": "false", "image": ["44", 0]}, "class_type": "Image Filter Adjustments", "_meta": {"title": "Image Filter Adjustments"}}, "85": {"inputs": {"method": "hm-mvgd-hm", "strength": 0.6500000000000001, "image_ref": ["83", 0], "image_target": ["21", 0]}, "class_type": "ColorMatch", "_meta": {"title": "Color Match"}}, "86": {"inputs": {"images": ["85", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "88": {"inputs": {"width": 2048, "height": 2048, "interpolation": "nearest", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["105", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "90": {"inputs": {"PowerLoraLoaderHeaderWidget": {"type": "PowerLoraLoaderHeaderWidget"}, "lora_1": {"on": true, "lora": "ASTRA_FLUX_LORA\\建筑-商办综合立面\\ASTRA_Flux_OC_Vbeta-2.safetensors", "strength": 0.45}, "lora_2": {"on": true, "lora": "FLUX\\Flux-Function\\FLUX.1-Turbo-Alpha.safetensors", "strength": 1}, "➕ Add Lora": "", "model": ["98", 0], "clip": ["99", 0]}, "class_type": "<PERSON> Lora <PERSON> (rgthree)", "_meta": {"title": "<PERSON> Lora <PERSON> (rgthree)"}}, "98": {"inputs": {"unet_name": "flux1_dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "99": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "100": {"inputs": {"model_name": "8x_NMKD-Typescale_175k.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "101": {"inputs": {"value": 1059630169839465, "mode": "randomize", "action": "randomize", "last_seed": 67977614402230}, "class_type": "easy globalSeed", "_meta": {"title": "EasyGlobalSeed"}}, "103": {"inputs": {"prompt": ""}, "class_type": "CR Prompt Text", "_meta": {"title": "prompt_input_01"}}, "105": {"inputs": {"base64_data": "", "image_output": "Preview", "save_prefix": "ComfyUI"}, "class_type": "easy loadImageBase64", "_meta": {"title": "redux_image_input"}}}